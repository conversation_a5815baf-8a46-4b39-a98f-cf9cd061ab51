

using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

public class MainMenu : MonoBehaviour
{
    #region Level Management
    public GameObject[] CareerLevelButtons;
    public GameObject[] FarmLevelButtons;
    public GameObject[] TouchanLevelButtons;
    public static int levlno;
    #endregion

    #region Audio System
    public AudioSource BtnSound;
    public AudioSource Sound;
    public Slider music;
    public Slider sound;
    #endregion

    #region UI Elements
    public Text coins;
    public GameObject lodingpanl;
    #endregion
    #region Unity Lifecycle
    void Start()
    {
        InitializeGame();
        InitializeAudio();
        InitializeLevels();
        foreach (GameObject btn in CareerLevelButtons)
        {
            btn.GetComponent<DOTweenAnimation>().DOPause();
        }
        foreach (GameObject btn in FarmLevelButtons)
        {
            btn.GetComponent<DOTweenAnimation>().DOPause();
        }
        foreach (GameObject btn in TouchanLevelButtons)
        {
            btn.GetComponent<DOTweenAnimation>().DOPause();
        }
    }
    #endregion

    #region Initialization Methods
    private void InitializeGame()
    {
        Time.timeScale = 1;
        coins.text = PlayerPrefs.GetInt("coins").ToString();
    }

    private void InitializeAudio()
    {
        music.value = PlayerPrefs.GetFloat("MusicVolume", 0.5f);
        sound.value = PlayerPrefs.GetFloat("SoundVolume", 0.5f);

        BtnSound.volume = music.value;
        Sound.volume = sound.value;
        music.onValueChanged.AddListener(OnMusicVolumeChanged);
        sound.onValueChanged.AddListener(OnSoundVolumeChanged);
    }

    private void InitializeLevels()
    {
        InitializeCareerLevels();
        InitializeFarmLevels();
        InitializeTouchanLevels();
    }
    private void InitializeCareerLevels()
    {
        // Pehle sabhi animations pause karo
        foreach (GameObject btn in CareerLevelButtons)
        {
            btn.GetComponent<DOTweenAnimation>().DOPause();
        }

        for (int i = 0; i < CareerLevelButtons.Length; i++)
        {
            // Get level completion status from PlayerPrefs
            int levelStatus = PlayerPrefs.GetInt("Career" + i, 0); // 0 = locked, 1 = available, 2 = completed

            // Get child objects for different states
            Transform completeChild = CareerLevelButtons[i].transform.GetChild(0); // Complete state
            Transform lockChild = CareerLevelButtons[i].transform.GetChild(1);     // Lock state
            Transform selectedChild = CareerLevelButtons[i].transform.GetChild(2); // Selected/Available state

            // Reset all child states
            completeChild.gameObject.SetActive(false);
            lockChild.gameObject.SetActive(false);
            selectedChild.gameObject.SetActive(false);

            // Set appropriate state based on level status
            if (levelStatus == 2) // Level completed
            {
                completeChild.gameObject.SetActive(true);
                CareerLevelButtons[i].GetComponent<Button>().interactable = true;
            }
            else if (levelStatus == 1 || i == 0) // Level available or first level (always available)
            {
                selectedChild.gameObject.SetActive(true);
                CareerLevelButtons[i].GetComponent<Button>().interactable = true;
                // Animation sirf selected child wale button ki play hogi
                CareerLevelButtons[i].GetComponent<DOTweenAnimation>().DOPlay();
                // Unlock next level if current level is completed
                if (i > 0 && PlayerPrefs.GetInt("Career" + (i - 1), 0) == 2)
                {
                    PlayerPrefs.SetInt("Career" + i, 1);
                }
            }
            else // Level locked
            {
                lockChild.gameObject.SetActive(true);
                CareerLevelButtons[i].GetComponent<Button>().interactable = false;
            }
        }
    }

    private void InitializeFarmLevels()
    {
        // Pehle sabhi animations pause karo
        foreach (GameObject btn in FarmLevelButtons)
        {
            btn.GetComponent<DOTweenAnimation>().DOPause();
        }

        for (int i = 0; i < FarmLevelButtons.Length; i++)
        {
            // Get level completion status from PlayerPrefs
            int levelStatus = PlayerPrefs.GetInt("Farm" + i, 0); // 0 = locked, 1 = available, 2 = completed

            // Get child objects for different states
            Transform completeChild = FarmLevelButtons[i].transform.GetChild(0); // Complete state
            Transform lockChild = FarmLevelButtons[i].transform.GetChild(1);     // Lock state
            Transform selectedChild = FarmLevelButtons[i].transform.GetChild(2); // Selected/Available state

            // Reset all child states
            completeChild.gameObject.SetActive(false);
            lockChild.gameObject.SetActive(false);
            selectedChild.gameObject.SetActive(false);

            // Set appropriate state based on level status
            if (levelStatus == 2) // Level completed
            {
                completeChild.gameObject.SetActive(true);
                FarmLevelButtons[i].GetComponent<Button>().interactable = true;
            }
            else if (levelStatus == 1 || i == 0) // Level available or first level (always available)
            {
                selectedChild.gameObject.SetActive(true);
                FarmLevelButtons[i].GetComponent<Button>().interactable = true;
                // Animation sirf selected child wale button ki play hogi
                FarmLevelButtons[i].GetComponent<DOTweenAnimation>().DOPlay();

                // Unlock next level if current level is completed
                if (i > 0 && PlayerPrefs.GetInt("Farm" + (i - 1), 0) == 2)
                {
                    PlayerPrefs.SetInt("Farm" + i, 1);
                }
            }
            else // Level locked
            {
                lockChild.gameObject.SetActive(true);
                FarmLevelButtons[i].GetComponent<Button>().interactable = false;
            }
        }
    }

    private void InitializeTouchanLevels()
    {
        // Pehle sabhi animations pause karo
        foreach (GameObject btn in TouchanLevelButtons)
        {
            btn.GetComponent<DOTweenAnimation>().DOPause();
        }

        for (int i = 0; i < TouchanLevelButtons.Length; i++)
        {
            // Get level completion status from PlayerPrefs
            int levelStatus = PlayerPrefs.GetInt("Touchan" + i, 0); // 0 = locked, 1 = available, 2 = completed

            // Get child objects for different states
            Transform completeChild = TouchanLevelButtons[i].transform.GetChild(0); // Complete state
            Transform lockChild = TouchanLevelButtons[i].transform.GetChild(1);     // Lock state
            Transform selectedChild = TouchanLevelButtons[i].transform.GetChild(2); // Selected/Available state

            // Reset all child states
            completeChild.gameObject.SetActive(false);
            lockChild.gameObject.SetActive(false);
            selectedChild.gameObject.SetActive(false);

            // Set appropriate state based on level status
            if (levelStatus == 2) // Level completed
            {
                completeChild.gameObject.SetActive(true);
                TouchanLevelButtons[i].GetComponent<Button>().interactable = true;
            }
            else if (levelStatus == 1 || i == 0) // Level available or first level (always available)
            {
                selectedChild.gameObject.SetActive(true);
                TouchanLevelButtons[i].GetComponent<Button>().interactable = true;
                // Animation sirf selected child wale button ki play hogi
                TouchanLevelButtons[i].GetComponent<DOTweenAnimation>().DOPlay();

                // Unlock next level if current level is completed
                if (i > 0 && PlayerPrefs.GetInt("Touchan" + (i - 1), 0) == 2)
                {
                    PlayerPrefs.SetInt("Touchan" + i, 1);
                }
            }
            else // Level locked
            {
                lockChild.gameObject.SetActive(true);
                TouchanLevelButtons[i].GetComponent<Button>().interactable = false;
            }
        }
    }
    #endregion

    #region Level Completion Methods
    /// <summary>
    /// Call this method when a Career level is completed
    /// </summary>
    /// <param name="levelIndex">Index of the completed level</param>
    public static void CompleteCareerLevel(int levelIndex)
    {
        PlayerPrefs.SetInt("Career" + levelIndex, 2); // Mark as completed

        // Unlock next level if it exists
        if (levelIndex + 1 < 10) // Assuming max 10 levels, adjust as needed
        {
            if (PlayerPrefs.GetInt("Career" + (levelIndex + 1), 0) == 0)
            {
                PlayerPrefs.SetInt("Career" + (levelIndex + 1), 1); // Unlock next level
            }
        }
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Call this method when a Farm level is completed
    /// </summary>
    /// <param name="levelIndex">Index of the completed level</param>
    public static void CompleteFarmLevel(int levelIndex)
    {
        PlayerPrefs.SetInt("Farm" + levelIndex, 2); // Mark as completed

        // Unlock next level if it exists
        if (levelIndex + 1 < 10) // Assuming max 10 levels, adjust as needed
        {
            if (PlayerPrefs.GetInt("Farm" + (levelIndex + 1), 0) == 0)
            {
                PlayerPrefs.SetInt("Farm" + (levelIndex + 1), 1); // Unlock next level
            }
        }
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Call this method when a Touchan level is completed
    /// </summary>
    /// <param name="levelIndex">Index of the completed level</param>
    public static void CompleteTouchanLevel(int levelIndex)
    {
        PlayerPrefs.SetInt("Touchan" + levelIndex, 2); // Mark as completed

        // Unlock next level if it exists
        if (levelIndex + 1 < 10) // Assuming max 10 levels, adjust as needed
        {
            if (PlayerPrefs.GetInt("Touchan" + (levelIndex + 1), 0) == 0)
            {
                PlayerPrefs.SetInt("Touchan" + (levelIndex + 1), 1); // Unlock next level
            }
        }
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Reset all level progress (for testing purposes)
    /// </summary>
    public static void ResetAllLevels()
    {
        // Reset Career levels
        for (int i = 0; i < 10; i++)
        {
            PlayerPrefs.DeleteKey("Career" + i);
        }

        // Reset Farm levels
        for (int i = 0; i < 10; i++)
        {
            PlayerPrefs.DeleteKey("Farm" + i);
        }

        // Reset Touchan levels
        for (int i = 0; i < 10; i++)
        {
            PlayerPrefs.DeleteKey("Touchan" + i);
        }

        PlayerPrefs.Save();
    }
    #endregion

    #region UI Navigation Methods
    public void yes()
    {
        Application.Quit();
    }

    #endregion
    #region Audio Settings Methods
    public void save()
    {

        PlayerPrefs.SetFloat("MusicVolume", music.value);
        PlayerPrefs.SetFloat("SoundVolume", sound.value);
        PlayerPrefs.Save();
    }

    public void OnMusicVolumeChanged(float value)
    {
        BtnSound.volume = value;
        PlayerPrefs.SetFloat("MusicVolume", value);
    }

    public void OnSoundVolumeChanged(float value)
    {
        Sound.volume = value;
        PlayerPrefs.SetFloat("SoundVolume", value);
    }
    #endregion
    #region Level Selection Methods
    public void Careerlevel(int Clevel)
    {
        // Selected button ki animation play karo
        if (Clevel < CareerLevelButtons.Length)
        {
            CareerLevelButtons[Clevel].GetComponent<DOTweenAnimation>().DOPlay();
        }

        levlno = Clevel;
        lodingpanl.SetActive(true);
        StartCoroutine(GamePlayC());
    }

    public void Farmlevel(int Flevel)
    {
        levlno = Flevel;
        lodingpanl.SetActive(true);
        StartCoroutine(FarmingC());

    }

    public void Touchanlevel(int Tlevel)
    {
        levlno = Tlevel;
        lodingpanl.SetActive(true);
        StartCoroutine(TouchanC());
    }
    #endregion

    #region Control Settings Methods
    public void Steer()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
    }

    public void Btns()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
    }

    public void Tilt()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
    }
    #endregion
    #region Coroutines
    IEnumerator FarmingC()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("Farming mod");
    }

    IEnumerator GamePlayC()
    {
        yield return new WaitForSeconds(1f);
        yield return new WaitForSeconds(3f);
        SceneManager.LoadScene("gameplay");
    }

    IEnumerator TouchanC()
    {
        yield return new WaitForSeconds(1f);
        yield return new WaitForSeconds(3f);
        SceneManager.LoadScene("tractortochan");
    }
    #endregion

    #region External Links Methods
    public void rateus()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.tractor.trolly.games.farming.game");
    }

    public void privacy()
    {
        Application.OpenURL("https://simulatorgames2022.blogspot.com/2023/04/privacy-policy.html");
    }

    public void moregams()
    {
        Application.OpenURL("https://play.google.com/store/apps/dev?id=6151632225219809775");
    }

    public void mudjeep()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.offroadjeep.mudjeep.offtheroad.jeepgame.simulator.offroad.driving.games");
    }

    public void trainadd()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.city.train.simulator.zt.game&hl=en");
    }
    #endregion

}
